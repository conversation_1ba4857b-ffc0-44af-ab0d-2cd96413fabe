package com.logictrue.word.service;

import org.apache.poi.xwpf.usermodel.*;

import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 创建包含表格的测试Word文档
 */
public class CreateTableTestDocument {

    public static void main(String[] args) {
        try {
            createTableTestDocument("/home/<USER>/tempFile/table_test.docx");
            System.out.println("包含表格的测试Word文档已创建!");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void createTableTestDocument(String filePath) throws IOException {
        try (XWPFDocument document = new XWPFDocument();
             FileOutputStream out = new FileOutputStream(filePath)) {

            // 创建标题
            XWPFParagraph titleParagraph = document.createParagraph();
            titleParagraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun titleRun = titleParagraph.createRun();
            titleRun.setText("表格转换测试文档");
            titleRun.setBold(true);
            titleRun.setFontSize(16);

            // 创建空行
            document.createParagraph();

            // 创建描述段落
            XWPFParagraph descParagraph = document.createParagraph();
            XWPFRun descRun = descParagraph.createRun();
            descRun.setText("以下是一个测试表格，用于验证Word转PDF时表格数据的正确转换：");

            // 创建空行
            document.createParagraph();

            // 创建表格
            createSampleTable(document);

            // 创建空行
            document.createParagraph();

            // 创建另一个表格
            createProductTable(document);

            // 保存文档
            document.write(out);
        }
    }

    /**
     * 创建示例表格
     */
    private static void createSampleTable(XWPFDocument document) {
        // 创建3行4列的表格
        XWPFTable table = document.createTable(3, 4);

        // 设置表格标题行
        XWPFTableRow headerRow = table.getRow(0);
        headerRow.getCell(0).setText("姓名");
        headerRow.getCell(1).setText("年龄");
        headerRow.getCell(2).setText("部门");
        headerRow.getCell(3).setText("职位");

        // 设置表头样式
        for (XWPFTableCell cell : headerRow.getTableCells()) {
            cell.setColor("E6E6FA"); // 浅紫色背景
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph.getRuns().get(0);
            run.setBold(true);
        }

        // 填充数据行
        XWPFTableRow row1 = table.getRow(1);
        row1.getCell(0).setText("Zhang San");
        row1.getCell(1).setText("28");
        row1.getCell(2).setText("IT Department");
        row1.getCell(3).setText("Engineer");

        XWPFTableRow row2 = table.getRow(2);
        row2.getCell(0).setText("Li Si");
        row2.getCell(1).setText("32");
        row2.getCell(2).setText("HR Department");
        row2.getCell(3).setText("Manager");

        // 设置数据行样式
        for (int i = 1; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            for (XWPFTableCell cell : row.getTableCells()) {
                XWPFParagraph paragraph = cell.getParagraphs().get(0);
                paragraph.setAlignment(ParagraphAlignment.CENTER);
            }
        }
    }

    /**
     * 创建产品信息表格
     */
    private static void createProductTable(XWPFDocument document) {
        // 添加表格标题
        XWPFParagraph tableTitleParagraph = document.createParagraph();
        XWPFRun tableTitleRun = tableTitleParagraph.createRun();
        tableTitleRun.setText("产品信息表");
        tableTitleRun.setBold(true);
        tableTitleRun.setFontSize(14);

        // 创建空行
        document.createParagraph();

        // 创建5行3列的表格
        XWPFTable productTable = document.createTable(5, 3);

        // 设置表格标题行
        XWPFTableRow headerRow = productTable.getRow(0);
        headerRow.getCell(0).setText("Product Name");
        headerRow.getCell(1).setText("Price");
        headerRow.getCell(2).setText("Stock");

        // 设置表头样式
        for (XWPFTableCell cell : headerRow.getTableCells()) {
            cell.setColor("FFE4B5"); // 浅橙色背景
            XWPFParagraph paragraph = cell.getParagraphs().get(0);
            paragraph.setAlignment(ParagraphAlignment.CENTER);
            XWPFRun run = paragraph.getRuns().get(0);
            run.setBold(true);
        }

        // 填充产品数据
        String[][] productData = {
            {"Laptop", "$999.99", "50"},
            {"Mouse", "$29.99", "200"},
            {"Keyboard", "$79.99", "150"},
            {"Monitor", "$299.99", "75"}
        };

        for (int i = 0; i < productData.length; i++) {
            XWPFTableRow row = productTable.getRow(i + 1);
            for (int j = 0; j < productData[i].length; j++) {
                row.getCell(j).setText(productData[i][j]);
                XWPFParagraph paragraph = row.getCell(j).getParagraphs().get(0);
                paragraph.setAlignment(ParagraphAlignment.CENTER);
            }
        }
    }
}
