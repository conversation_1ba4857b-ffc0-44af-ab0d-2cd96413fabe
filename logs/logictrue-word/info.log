09:01:02.933 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:01:02.939 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
10:02:15.751 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 500640 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
10:02:15.753 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:02:16.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
10:02:16.530 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:02:16.530 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:02:16.575 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:02:16.998 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:02:17.200 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:02:17.486 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:02:17.487 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:02:17.817 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:02:17.844 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:02:17.845 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:02:17.909 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:02:17.914 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:02:17.916 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:02:17.917 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:02:17.917 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:02:17.918 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:02:17.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
10:02:17.967 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.526 seconds (JVM running for 2.845)
10:02:48.846 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:10:25.497 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
check_user_name
from drl_work_check_records
where car_id = ?
order by sort
10:10:25.497 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：null
10:10:54.009 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
check_user_name
from drl_work_check_records
where car_id = ?
order by sort
10:10:54.010 [http-nio-9550-exec-10] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:16:21.789 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:16:21.790 [http-nio-9550-exec-2] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:36:53.643 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:36:53.643 [http-nio-9550-exec-1] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:05.722 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:05.722 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:19.508 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:19.508 [http-nio-9550-exec-4] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:37:37.118 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:37:37.118 [http-nio-9550-exec-6] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:48:07.506 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:48:07.506 [http-nio-9550-exec-7] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
10:53:20.409 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,31] - 执行SQL：select 
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records
where car_id = ?
order by sort
10:53:20.409 [http-nio-9550-exec-3] INFO  /word/根据车辆id查询检验记录(/word/getCheckRecord) - [handleLog,36] - SQL参数：0822(String)
11:13:32.861 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:13:32.861 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:13:32.862 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:13:33.194 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:13:33.194 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:13:33.264 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 935px (14025twips)
11:13:33.341 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:13:33.343 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:13:33.347 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:13:33.348 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:13:33.349 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:13:33.352 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:13:33.353 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:13:33.354 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:13:33.433 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2799 bytes
11:13:33.441 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111333.docx, 大小: 2799 bytes
11:15:44.983 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:15:44.984 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:15:44.985 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:15:44.988 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:15:44.989 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:15:44.990 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 810px (12150twips)
11:15:45.028 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:15:45.030 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:15:45.032 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:15:45.033 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:15:45.034 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:15:45.035 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:15:45.036 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:15:45.037 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:15:45.051 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:15:45.061 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111545.docx, 大小: 2798 bytes
11:17:07.830 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:17:07.832 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:17:07.834 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:17:07.837 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:17:07.838 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:17:07.842 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 840px (12600twips)
11:17:07.851 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:17:07.854 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:17:07.855 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:17:07.860 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:17:07.862 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:17:07.863 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:17:07.864 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:17:07.867 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:17:07.881 [http-nio-9550-exec-3] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:17:07.886 [http-nio-9550-exec-3] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111707.docx, 大小: 2798 bytes
11:19:18.587 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:19:18.588 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:19:18.588 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:19:18.589 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:19:18.589 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:19:18.591 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 895px (13425twips)
11:19:18.595 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:19:18.596 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:19:18.597 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:19:18.598 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:19:18.598 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:19:18.599 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:19:18.605 [http-nio-9550-exec-6] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2799 bytes
11:19:18.609 [http-nio-9550-exec-6] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_111918.docx, 大小: 2799 bytes
11:20:21.325 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,74] - 接收到新JSON格式的表格导出请求，标题: null
11:20:21.326 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,76] - 表头合并数量: 7, 数据合并数量: 0
11:20:21.326 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1533] - 开始导出新JSON格式Word文档，表格标题: null
11:20:21.327 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setPageOrientation,761] - 已设置文档为横向纸张
11:20:21.328 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [createTableFromNewJsonFormat,1577] - 创建新JSON格式表格，总行数: 2, 总列数: 8
11:20:21.329 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [setTableStyleFromJsonConfig,1648] - 设置表格总宽度: 880px (13200twips)
11:20:21.332 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyMergesFromJsonFormat,1754] - 应用JSON格式表头合并单元格，数量: 7
11:20:21.333 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[0-0], 跨行: 2, 跨列: 1, 内容: '检查工序
名称'
11:20:21.334 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[1-1], 跨行: 2, 跨列: 1, 内容: '检 查 项 目 及 技 术 条 件'
11:20:21.335 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[2-2], 跨行: 2, 跨列: 1, 内容: '实 际 检 查 结 果'
11:20:21.337 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-0], 列[3-4], 跨行: 1, 跨列: 2, 内容: '完工'
11:20:21.338 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[5-5], 跨行: 2, 跨列: 1, 内容: '操作员'
11:20:21.339 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[6-6], 跨行: 2, 跨列: 1, 内容: '班组长'
11:20:21.340 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [applyJsonHeaderMerge,1795] - 应用表头合并: 行[0-1], 列[7-7], 跨行: 2, 跨列: 1, 内容: '检验员'
11:20:21.344 [http-nio-9550-exec-5] INFO  c.l.w.s.WordExportService - [exportNewJsonFormatToWord,1554] - 新JSON格式Word文档导出完成，文件大小: 2798 bytes
11:20:21.347 [http-nio-9550-exec-5] INFO  c.l.w.c.WordExportController - [exportNewJsonFormat,93] - 新JSON格式Word文档导出成功，文件名: %E8%A1%A8%E6%A0%BC%E5%AF%BC%E5%87%BA_20250822_112021.docx, 大小: 2798 bytes
