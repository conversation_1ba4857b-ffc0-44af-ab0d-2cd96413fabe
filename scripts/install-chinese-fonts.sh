#!/bin/bash

# Linux中文字体安装脚本
# 解决Aspose Words在Linux下中文不显示的问题

echo "=== Linux中文字体安装脚本 ==="

# 检查系统类型
if [ -f /etc/debian_version ]; then
    DISTRO="debian"
    echo "检测到Debian/Ubuntu系统"
elif [ -f /etc/redhat-release ]; then
    DISTRO="redhat"
    echo "检测到RedHat/CentOS系统"
else
    echo "未知系统类型，尝试通用安装方法"
    DISTRO="unknown"
fi

# 创建字体目录
echo "创建字体目录..."
sudo mkdir -p /usr/share/fonts/truetype/chinese
sudo mkdir -p /usr/local/share/fonts/chinese

# 安装基础字体包
echo "安装基础字体包..."
if [ "$DISTRO" = "debian" ]; then
    # Debian/Ubuntu系统
    sudo apt-get update
    sudo apt-get install -y fonts-wqy-zenhei fonts-wqy-microhei
    sudo apt-get install -y fonts-arphic-ukai fonts-arphic-uming
    sudo apt-get install -y fonts-noto-cjk fonts-noto-cjk-extra
    sudo apt-get install -y fontconfig
elif [ "$DISTRO" = "redhat" ]; then
    # RedHat/CentOS系统
    sudo yum install -y wqy-zenhei-fonts wqy-microhei-fonts
    sudo yum install -y cjkuni-ukai-fonts cjkuni-uming-fonts
    sudo yum install -y google-noto-cjk-fonts
    sudo yum install -y fontconfig
fi

# 下载常用中文字体（如果网络可用）
echo "下载常用中文字体..."
FONT_DIR="/tmp/chinese_fonts"
mkdir -p $FONT_DIR

# 检查是否有wget或curl
if command -v wget > /dev/null; then
    DOWNLOAD_CMD="wget -O"
elif command -v curl > /dev/null; then
    DOWNLOAD_CMD="curl -L -o"
else
    echo "警告: 未找到wget或curl，跳过字体下载"
    DOWNLOAD_CMD=""
fi

# 如果有下载工具，尝试下载字体
if [ ! -z "$DOWNLOAD_CMD" ]; then
    echo "尝试下载SimSun字体..."
    # 注意：这里需要替换为实际可用的字体下载链接
    # $DOWNLOAD_CMD $FONT_DIR/simsun.ttc "https://example.com/fonts/simsun.ttc"
    
    echo "尝试下载SimHei字体..."
    # $DOWNLOAD_CMD $FONT_DIR/simhei.ttf "https://example.com/fonts/simhei.ttf"
    
    echo "字体下载完成（如果有可用链接）"
fi

# 复制Windows字体（如果存在Windows分区）
echo "检查Windows字体..."
WINDOWS_FONTS_PATHS=(
    "/mnt/c/Windows/Fonts"
    "/media/*/Windows/Fonts"
    "/mnt/windows/Windows/Fonts"
)

for path in "${WINDOWS_FONTS_PATHS[@]}"; do
    if [ -d "$path" ]; then
        echo "找到Windows字体目录: $path"
        echo "复制中文字体..."
        
        # 复制常用中文字体
        for font in simsun.ttc simhei.ttf msyh.ttc msyhbd.ttc; do
            if [ -f "$path/$font" ]; then
                echo "复制字体: $font"
                sudo cp "$path/$font" /usr/share/fonts/truetype/chinese/
            fi
        done
        break
    fi
done

# 创建项目字体目录
echo "创建项目字体目录..."
PROJECT_FONT_DIR="src/main/resources/fonts"
mkdir -p $PROJECT_FONT_DIR

# 创建字体配置文件
echo "创建字体配置文件..."
cat > $PROJECT_FONT_DIR/fonts.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
    <!-- 中文字体配置 -->
    <alias>
        <family>serif</family>
        <prefer>
            <family>SimSun</family>
            <family>宋体</family>
            <family>WenQuanYi Zen Hei</family>
            <family>DejaVu Serif</family>
        </prefer>
    </alias>
    
    <alias>
        <family>sans-serif</family>
        <prefer>
            <family>SimHei</family>
            <family>黑体</family>
            <family>Microsoft YaHei</family>
            <family>微软雅黑</family>
            <family>WenQuanYi Zen Hei</family>
            <family>DejaVu Sans</family>
        </prefer>
    </alias>
    
    <alias>
        <family>monospace</family>
        <prefer>
            <family>SimSun</family>
            <family>WenQuanYi Zen Hei Mono</family>
            <family>DejaVu Sans Mono</family>
        </prefer>
    </alias>
</fontconfig>
EOF

# 刷新字体缓存
echo "刷新字体缓存..."
sudo fc-cache -fv

# 检查字体安装情况
echo "检查中文字体安装情况..."
echo "已安装的中文字体:"
fc-list :lang=zh | head -10

# 创建字体测试脚本
echo "创建字体测试脚本..."
cat > test-fonts.sh << 'EOF'
#!/bin/bash
echo "=== 字体测试 ==="
echo "系统中文字体列表:"
fc-list :lang=zh-cn | grep -E "(SimSun|SimHei|Microsoft|WenQuanYi|Noto)" | head -5

echo ""
echo "字体配置信息:"
fc-match "SimSun"
fc-match "SimHei"
fc-match "Microsoft YaHei"

echo ""
echo "字体目录内容:"
ls -la /usr/share/fonts/truetype/chinese/ 2>/dev/null || echo "中文字体目录不存在"
EOF

chmod +x test-fonts.sh

echo ""
echo "=== 安装完成 ==="
echo "1. 字体已安装到系统目录"
echo "2. 字体缓存已刷新"
echo "3. 项目字体配置已创建"
echo ""
echo "测试命令:"
echo "  ./test-fonts.sh  # 测试字体安装"
echo "  fc-list :lang=zh # 查看中文字体"
echo ""
echo "如果仍有问题，请检查:"
echo "1. 字体文件权限是否正确"
echo "2. 是否需要重启应用程序"
echo "3. Java应用是否能访问系统字体"
