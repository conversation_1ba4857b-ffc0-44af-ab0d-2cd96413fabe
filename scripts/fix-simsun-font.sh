#!/bin/bash

# SimSun字体修复脚本
# 专门解决Linux下SimSun字体在PDF转换中不显示的问题

echo "=== SimSun字体修复脚本 ==="

# 检查系统类型
OS=$(uname -s)
echo "检测到系统: $OS"

# 创建字体目录
echo "创建字体目录..."
sudo mkdir -p /usr/share/fonts/truetype/chinese
mkdir -p src/main/resources/fonts

# 函数：检查字体文件
check_font_file() {
    local font_path="$1"
    if [ -f "$font_path" ]; then
        echo "✓ 找到字体文件: $font_path"
        echo "  文件大小: $(ls -lh "$font_path" | awk '{print $5}')"
        return 0
    else
        echo "✗ 未找到: $font_path"
        return 1
    fi
}

# 函数：复制字体文件
copy_font_file() {
    local src="$1"
    local dst="$2"
    
    if [ -f "$src" ]; then
        echo "复制字体文件: $src -> $dst"
        sudo cp "$src" "$dst"
        sudo chmod 644 "$dst"
        return 0
    fi
    return 1
}

echo ""
echo "--- 查找SimSun字体文件 ---"

# 可能的SimSun字体路径
SIMSUN_PATHS=(
    "/mnt/c/Windows/Fonts/simsun.ttc"
    "/mnt/c/Windows/Fonts/SimSun.ttf"
    "C:/Windows/Fonts/simsun.ttc"
    "C:/Windows/Fonts/SimSun.ttf"
    "/usr/share/fonts/truetype/chinese/simsun.ttc"
    "/usr/share/fonts/simsun.ttc"
    "/usr/local/share/fonts/simsun.ttc"
    "$HOME/.fonts/simsun.ttc"
)

FOUND_SIMSUN=""
for path in "${SIMSUN_PATHS[@]}"; do
    if check_font_file "$path"; then
        FOUND_SIMSUN="$path"
        break
    fi
done

if [ -z "$FOUND_SIMSUN" ]; then
    echo ""
    echo "❌ 未找到SimSun字体文件"
    echo ""
    echo "解决方案："
    echo "1. 如果您有Windows系统，请复制以下文件："
    echo "   C:\\Windows\\Fonts\\simsun.ttc"
    echo "   到Linux系统的以下位置之一："
    echo "   - /usr/share/fonts/truetype/chinese/"
    echo "   - src/main/resources/fonts/"
    echo ""
    echo "2. 或者下载SimSun字体文件："
    echo "   wget -O simsun.ttc [字体下载链接]"
    echo "   sudo cp simsun.ttc /usr/share/fonts/truetype/chinese/"
    echo ""
    echo "3. 使用替代字体："
    echo "   sudo apt-get install fonts-wqy-zenhei fonts-wqy-microhei"
    echo ""
else
    echo ""
    echo "✅ 找到SimSun字体: $FOUND_SIMSUN"
    
    # 复制到系统字体目录
    if [ "$FOUND_SIMSUN" != "/usr/share/fonts/truetype/chinese/simsun.ttc" ]; then
        copy_font_file "$FOUND_SIMSUN" "/usr/share/fonts/truetype/chinese/simsun.ttc"
    fi
    
    # 复制到项目字体目录
    copy_font_file "$FOUND_SIMSUN" "src/main/resources/fonts/simsun.ttc"
fi

echo ""
echo "--- 安装替代中文字体 ---"

# 根据系统类型安装字体
if command -v apt-get > /dev/null; then
    echo "使用apt-get安装中文字体..."
    sudo apt-get update
    sudo apt-get install -y fonts-wqy-zenhei fonts-wqy-microhei
    sudo apt-get install -y fonts-arphic-ukai fonts-arphic-uming
    sudo apt-get install -y fonts-noto-cjk
elif command -v yum > /dev/null; then
    echo "使用yum安装中文字体..."
    sudo yum install -y wqy-zenhei-fonts wqy-microhei-fonts
    sudo yum install -y cjkuni-ukai-fonts cjkuni-uming-fonts
elif command -v dnf > /dev/null; then
    echo "使用dnf安装中文字体..."
    sudo dnf install -y wqy-zenhei-fonts wqy-microhei-fonts
    sudo dnf install -y cjkuni-ukai-fonts cjkuni-uming-fonts
else
    echo "未知的包管理器，请手动安装中文字体"
fi

echo ""
echo "--- 配置字体权限 ---"

# 设置字体文件权限
sudo find /usr/share/fonts/truetype/chinese/ -name "*.ttc" -o -name "*.ttf" -o -name "*.otf" | while read font_file; do
    if [ -f "$font_file" ]; then
        sudo chmod 644 "$font_file"
        echo "设置权限: $font_file"
    fi
done

echo ""
echo "--- 刷新字体缓存 ---"
sudo fc-cache -fv

echo ""
echo "--- 验证字体安装 ---"

# 检查SimSun字体
echo "检查SimSun字体:"
fc-list | grep -i simsun | head -3

echo ""
echo "检查中文字体:"
fc-list :lang=zh | head -5

echo ""
echo "--- 创建字体测试文件 ---"

# 创建字体测试脚本
cat > test-simsun-font.sh << 'EOF'
#!/bin/bash
echo "=== SimSun字体测试 ==="

echo "1. 系统中的SimSun字体:"
fc-list | grep -i simsun

echo ""
echo "2. 中文字体列表:"
fc-list :lang=zh | grep -E "(SimSun|simsun|宋体)" | head -3

echo ""
echo "3. 字体文件检查:"
for path in "/usr/share/fonts/truetype/chinese/simsun.ttc" "src/main/resources/fonts/simsun.ttc"; do
    if [ -f "$path" ]; then
        echo "✓ $path ($(ls -lh "$path" | awk '{print $5}'))"
    else
        echo "✗ $path"
    fi
done

echo ""
echo "4. 运行Java字体测试:"
echo "   java SimSunFontTest"
EOF

chmod +x test-simsun-font.sh

echo ""
echo "--- 创建Java字体配置 ---"

# 创建Java字体配置文件
mkdir -p src/main/resources
cat > src/main/resources/font-config.properties << 'EOF'
# SimSun字体配置
font.simsun.paths=/usr/share/fonts/truetype/chinese/simsun.ttc,src/main/resources/fonts/simsun.ttc
font.chinese.fallback=WenQuanYi Zen Hei,DejaVu Sans,Liberation Sans

# 字体替换规则
font.substitution.simsun=SimSun,宋体,WenQuanYi Zen Hei
font.substitution.chinese=SimSun,SimHei,Microsoft YaHei,WenQuanYi Zen Hei
EOF

echo ""
echo "=== 修复完成 ==="
echo ""
echo "执行的操作:"
echo "1. ✓ 创建了字体目录"
echo "2. ✓ 查找并复制了SimSun字体文件"
echo "3. ✓ 安装了替代中文字体"
echo "4. ✓ 设置了字体权限"
echo "5. ✓ 刷新了字体缓存"
echo "6. ✓ 创建了测试脚本"
echo ""
echo "测试命令:"
echo "  ./test-simsun-font.sh     # 测试字体安装"
echo "  java SimSunFontTest       # Java字体测试"
echo ""
echo "如果问题仍然存在:"
echo "1. 检查Java应用是否有权限访问字体文件"
echo "2. 确认Aspose Words版本是否支持当前字体"
echo "3. 尝试使用字体替换规则"
echo "4. 检查PDF查看器是否支持嵌入字体"
